import {Directive, forwardRef} from '@angular/core';
import {AbstractControl, NG_VALIDATORS, Validator} from "@angular/forms";

@Directive({
  selector: '[campoTelefone][ngModel]',
  providers: [
    { provide: NG_VALIDATORS, useExisting: forwardRef(() => CampoTelefoneValidator), multi: true }
  ]
})
export class CampoTelefoneValidator  implements  Validator {
  constructor() {   }

  validate(c: AbstractControl): { [key: string]: any } {
    let valido = this.telefoneValido(c.value);

    if(valido) return null;

    return { 'campoTelefone': true }

  }

  telefoneValido(telefone){
    if(!telefone) return false;

    telefone = this.unmask(telefone);

    // Aceita telefones fixos (10 dígitos) e celulares (11 dígitos)
    // Fixo: [1-9]{2}[2-5][0-9]{7} - DDD + número fixo (8 dígitos começando com 2-5)
    // Celular: [1-9]{2}9[6-9][0-9]{7} - DDD + 9 + número celular (8 dígitos começando com 6-9)
    const regexFixo = /^[1-9]{2}[2-5][0-9]{7}$/;
    const regexCelular = /^[1-9]{2}9[6-9][0-9]{7}$/;

    return regexFixo.test(telefone) || regexCelular.test(telefone);
  }

  unmask(val) {
    return val.replace(/\D+/g, '');
  }

}
