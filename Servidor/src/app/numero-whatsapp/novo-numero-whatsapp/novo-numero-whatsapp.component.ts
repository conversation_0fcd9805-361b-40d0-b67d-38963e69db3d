import {Component, OnInit, ViewChild} from '@angular/core';
import {FormaDePagamento} from "../../pedidos/objetos/FormaDePagamento";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {NgForm} from "@angular/forms";
import {FormasDePagamentoService} from "../../services/formas-de-pagamento.service";
import {NumerosWhatsappService} from "../../services/numeros-whatsapp.service";
import {NumeroWhatsapp} from "../../pedidos/objetos/NumeroWhatsapp";

@Component({
  selector: 'app-novo-numero-whatsapp',
  templateUrl: './novo-numero-whatsapp.component.html',
  styleUrls: ['./novo-numero-whatsapp.component.scss']
})
export class NovoNumeroWhatsappComponent implements OnInit {
  numeroWhatsapp: NumeroWhatsapp;
  windowRef: DialogRef;
  empresa: any;
  mensagemErro = '';
  telefoneFormatado: string = '';
  @ViewChild('frm',  {static: false} ) frm: NgForm;
  enviando = false;

  constructor(private dialogService: DialogService, private numerosWhatsappService: NumerosWhatsappService) {
    this.numeroWhatsapp = new NumeroWhatsapp();
  }

  ngOnInit(): void {
    // Se estiver editando, formatar o telefone existente
    if (this.numeroWhatsapp.whatsapp) {
      this.telefoneFormatado = this.formatarTelefone(this.numeroWhatsapp.whatsapp);
    }
  }

  onSalvar() {
    delete this.mensagemErro;
    if(!this.frm.valid){
      return;
    }

    // Garantir que o telefone está limpo antes de salvar
    this.numeroWhatsapp.whatsapp = this.limparTelefone(this.telefoneFormatado);

    this.enviando = true;
    this.numerosWhatsappService.salveNumeroWhatsapp(this.numeroWhatsapp, this.empresa).then( (resposta) => {
      this.windowRef.close();
      this.enviando = false;
    }).catch( erro => {
      this.mensagemErro = erro;
      this.enviando = false;
    });
  }

  remova() {

  }

  fechar() {
    this.windowRef.close();
  }

  // Métodos de validação e formatação de telefone
  onTelefoneInput(event: any) {
    const value = event.target.value;
    const numeroLimpo = this.limparTelefone(value);

    // Aplica máscara dinâmica baseada no tamanho
    this.telefoneFormatado = this.aplicarMascaraDinamica(numeroLimpo);

    // Atualiza o valor no campo
    event.target.value = this.telefoneFormatado;

    // Remove formatação para salvar apenas números
    this.numeroWhatsapp.whatsapp = numeroLimpo;
  }

  onTelefoneBlur() {
    // Reaplica a formatação final
    if (this.telefoneFormatado) {
      const numeroLimpo = this.limparTelefone(this.telefoneFormatado);
      this.telefoneFormatado = this.aplicarMascaraDinamica(numeroLimpo);
      this.numeroWhatsapp.whatsapp = numeroLimpo;
    }
  }

  limparTelefone(telefone: string): string {
    if (!telefone) return '';
    // Remove todos os caracteres não numéricos
    return telefone.replace(/\D/g, '');
  }

  aplicarMascaraDinamica(numeroLimpo: string): string {
    if (!numeroLimpo) return '';

    // Limita a 11 dígitos
    if (numeroLimpo.length > 11) {
      numeroLimpo = numeroLimpo.substring(0, 11);
    }

    // Aplica formatação baseada no tamanho
    if (numeroLimpo.length <= 2) {
      return `(${numeroLimpo}`;
    } else if (numeroLimpo.length <= 6) {
      return `(${numeroLimpo.substring(0, 2)}) ${numeroLimpo.substring(2)}`;
    } else if (numeroLimpo.length <= 10) {
      // Formato fixo: (XX) XXXX-XXXX
      return `(${numeroLimpo.substring(0, 2)}) ${numeroLimpo.substring(2, 6)}-${numeroLimpo.substring(6)}`;
    } else {
      // Formato celular: (XX) XXXXX-XXXX
      return `(${numeroLimpo.substring(0, 2)}) ${numeroLimpo.substring(2, 7)}-${numeroLimpo.substring(7)}`;
    }
  }

  formatarTelefone(telefone: string): string {
    if (!telefone) return '';
    const numeroLimpo = telefone.replace(/\D/g, '');
    return this.aplicarMascaraDinamica(numeroLimpo);
  }

  validarTelefone(telefone: string): boolean {
    if (!telefone) return false;
    const telefoneLimpo = this.limparTelefone(telefone);

    // Validação para telefone brasileiro: aceita fixo (10 dígitos) e celular (11 dígitos)
    // Fixo: [1-9]{2}[2-5][0-9]{7} - DDD + número fixo (8 dígitos começando com 2-5)
    // Celular: [1-9]{2}9[6-9][0-9]{7} - DDD + 9 + número celular (8 dígitos começando com 6-9)
    const regexFixo = /^[1-9]{2}[2-5][0-9]{7}$/;
    const regexCelular = /^[1-9]{2}9[6-9][0-9]{7}$/;

    return regexFixo.test(telefoneLimpo) || regexCelular.test(telefoneLimpo);
  }
}
