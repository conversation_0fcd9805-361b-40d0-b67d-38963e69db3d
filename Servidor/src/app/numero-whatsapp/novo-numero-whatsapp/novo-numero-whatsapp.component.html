<form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="onSalvar()">

  <div class="alert alert-danger" role="alert" *ngIf="mensagemErro">
    <i class="fas fa-exclamation-triangle"></i> <span class="msg-erro">
    {{mensagemErro}}
  </span>
  </div>

  <div class="row">
    <div class="form-group mb-3 col">
      <label for="whatsapp" class="label-modern">
        <i class="fab fa-whatsapp text-success mr-2"></i>
        Número WhatsApp
      </label>

      <div class="input-group-modern">
        <div class="input-icon">
          <i class="fab fa-whatsapp"></i>
        </div>
        <input
          type="tel"
          id="whatsapp"
          name="whatsapp"
          class="form-control form-control-modern"
          [(ngModel)]="telefoneFormatado"
          placeholder="(11) 99999-9999 ou (11) 9999-9999"
          autocomplete="off"
          appAutoFocus [autoFocus]="true"
          required
          campoTelefone
          #whatsapp="ngModel"
          (blur)="onTelefoneBlur()"
          (input)="onTelefoneInput($event)">
      </div>

      <div class="invalid-feedback-modern" *ngIf="whatsapp.invalid && whatsapp.touched">
        <div *ngIf="whatsapp.errors?.required" class="error-item">
          <i class="fas fa-exclamation-circle"></i>
          Número WhatsApp é obrigatório
        </div>
        <div *ngIf="whatsapp.errors?.campoTelefone" class="error-item">
          <i class="fas fa-exclamation-triangle"></i>
          Formato inválido. Use: (11) 99999-9999 ou (11) 9999-9999
        </div>
      </div>

      <small class="form-text text-muted mt-2">
        <i class="fas fa-info-circle mr-1"></i>
        Digite números de celular (11 dígitos) ou fixo (10 dígitos) brasileiro
      </small>
    </div>
  </div>

  <div class="row">
    <div class="form-group mb-3 col">
      <div class="checkbox-group-modern">
        <input name="ocultar" id="ocultar" class="checkbox-modern" type="checkbox"
               kendoCheckBox [(ngModel)]="numeroWhatsapp.ocultar"/>
        <label class="checkbox-label-modern" for="ocultar">
          <i class="fas fa-eye-slash mr-2 text-muted"></i>
          Ocultar número no cardápio
          <small class="d-block text-muted mt-1">
            O número não será exibido publicamente para clientes
          </small>
        </label>
      </div>
    </div>
  </div>

  <div class="modal-footer-modern">
    <button type="button" (click)="fechar()" class="btn-modern btn-secondary">
      <i class="fas fa-times mr-2"></i>
      Cancelar
    </button>
    <button type="submit" class="btn-modern btn-primary" [disabled]="enviando || whatsapp.invalid">
      <i class="fas fa-spinner fa-spin mr-2" *ngIf="enviando"></i>
      <i class="fas fa-save mr-2" *ngIf="!enviando"></i>
      <span *ngIf="!enviando">Salvar</span>
      <span *ngIf="enviando">Salvando...</span>
    </button>
  </div>
</form>
