import {Resposta} from "../utils/Resposta";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Empresa} from "../domain/Empresa";
import {Router} from "express";
import {MapeadorDeNumeroWhatsapp} from "../mapeadores/MapeadorDeNumeroWhatsapp";
import {NumeroWhatsapp} from "../domain/NumeroWhatsapp";

const router: Router = Router();

router.post('/', async (req: any, res) => {
  const dados: any = req.body;

  insiraOuAtualize(dados).then( (resposta) => {
    res.json(resposta);
  }).catch( erro => {
    res.json(erro);
  });
});

router.put('/', async (req: any, res) => {
  const dados: any = req.body;

  insiraOuAtualize(dados).then( (resposta) => {
    res.json(resposta);
  }).catch( erro => {
    res.json(erro);
  });
});

router.get('/:id', async(req: any, res) => {
  const idEmpresa = req.params.id;
  let contexto = require('domain').active.contexto;

  new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa)).then( (empresa) => {
    if( !empresa ) {
      res.json({
        sucesso: true
      });
      return;
    }

    contexto.empresa = empresa;
    contexto.idEmpresa = empresa.id;

    const mapeador = new MapeadorDeNumeroWhatsapp();

    mapeador.listeAsync({
    }).then( (numeros) => {
      // Marcar qual número é usado para campanhas
      if (numeros && empresa.numeroWhatsappCampanhasId) {
        numeros.forEach((numero: any) => {
          numero.usadoParaCampanhas = (numero.id === empresa.numeroWhatsappCampanhasId);
        });
      }

      res.json({
        sucesso: true,
        data: numeros
      });
    });
  });
});

router.delete('/:id', async(req: any, res) => {
  const id = req.params.id;
  const empresa: Empresa = req.empresa;

  const numeroWhatsapp = new NumeroWhatsapp();
  numeroWhatsapp.id = id;
  numeroWhatsapp.empresa = empresa;

  const mapeadorDeNumeroWhatsapp = new MapeadorDeNumeroWhatsapp();

  mapeadorDeNumeroWhatsapp.marqueComoRemovido(numeroWhatsapp).then( () => {
    new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso('Número Whatsapp removido com sucesso'));
  });
});

router.post('/marquePrincipal', async(req: any, res) => {
  const dados: any = req.body;
  const idEmpresa = dados.empresa.id;
  const numero: any = dados.numero;

  let contexto = require('domain').active.contexto;

  new MapeadorDeEmpresa().selecioneSync(Number(idEmpresa)).then( (empresa) => {
    contexto.empresa = empresa;
    contexto.idEmpresa = empresa.id;

    const mapeadorDeNumeroWhatsapp = new MapeadorDeNumeroWhatsapp();

    mapeadorDeNumeroWhatsapp.selecioneSync({
      id: numero.id
    }).then((numeroExistente: NumeroWhatsapp) => {
      console.log('numero: ' + numeroExistente);

      mapeadorDeNumeroWhatsapp.marquePrincipal(numeroExistente).then(() => {
        new MapeadorDeEmpresa().removaDasCaches(empresa);
        res.json(Resposta.sucesso(`Número Whatsapp ${numeroExistente.whatsapp} agora é o principal`));
      });
    });
  });
});

function insiraOuAtualize(objetoDados: any) {
  return new Promise( (resolve, reject) => {
    const mapeadorDeEmpresa = new MapeadorDeEmpresa();

    mapeadorDeEmpresa.selecioneSync({id: objetoDados.empresa.id}).then( empresa => {
      let contexto = require('domain').active.contexto;
      contexto.empresa = empresa;
      contexto.idEmpresa = empresa.id;

      const mapeadorDeNumeroWhatsapp = new MapeadorDeNumeroWhatsapp();

      const dados = objetoDados.numero;
      mapeadorDeNumeroWhatsapp.selecioneSync({
        idEmpresa: empresa.id,
        whatsapp: dados.whatsapp,
        removidos: true
      }).then((numeroExistente: NumeroWhatsapp) => {
        if (numeroExistente && dados.id !== numeroExistente.id && numeroExistente.removido === false) {
          resolve(Resposta.erro('Já existe um Número Whatsapp com esse número!'));
          return;
        }

        const numeroWhatsapp = new NumeroWhatsapp();
        numeroWhatsapp.id = dados.id;
        numeroWhatsapp.whatsapp = dados.whatsapp;
        numeroWhatsapp.principal = dados.principal;
        numeroWhatsapp.ocultar = dados.ocultar;
        numeroWhatsapp.removido = false;

        if (numeroExistente) {
          numeroWhatsapp.id = numeroExistente.id;
          numeroWhatsapp.principal = numeroExistente.principal;
        }

        if (numeroWhatsapp.principal === undefined || numeroWhatsapp.principal === null) {
          numeroWhatsapp.principal = false;
        }

        if (numeroWhatsapp.id) {
          mapeadorDeNumeroWhatsapp.atualizeSync(numeroWhatsapp).then(() => {
            new MapeadorDeEmpresa().removaDasCaches(empresa);
            resolve(Resposta.sucesso(numeroWhatsapp));
          }).catch((erro: Error) => {
            resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
          });
        } else {
          mapeadorDeNumeroWhatsapp.insiraGraph(numeroWhatsapp).then((novoId: number) => {
            numeroWhatsapp.id = novoId;
            new MapeadorDeEmpresa().removaDasCaches(empresa);
            resolve(Resposta.sucesso(numeroWhatsapp));
          }).catch((erro: Error) => {
            resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
          });
        }
      });
    });
  });
}

export const NumeroWhatsappController: Router = router;
